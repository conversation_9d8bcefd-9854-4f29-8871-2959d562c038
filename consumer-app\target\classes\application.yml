spring:
  application:
    name: youtube-consumer
  
  # Kafka configuration
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:9092}
    consumer:
      group-id: youtube-analytics-group-v2
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      properties:
        spring.json.trusted.packages: "*"
        spring.json.default.type: "com.youtube.shared.model.ChannelMetadata"
        spring.json.use.type.headers: false
        spring.json.value.default.type: "com.youtube.shared.model.ChannelMetadata"

# Server configuration
server:
  port: 8082

# Logging configuration
logging:
  level:
    com.youtube.consumer: DEBUG
    org.springframework.kafka: INFO 