package com.youtube.shared.model;

import java.time.LocalDateTime;

public class ChannelMetadata {
    private String videoId;
    private String channelName;
    private String channelId;
    private Long subscribersCount;
    private Long totalVideos;
    private Long totalComments;
    private Long totalLikes;
    private LocalDateTime lastUpdated;

    // Default constructor
    public ChannelMetadata() {}

    // Constructor with all fields
    public ChannelMetadata(String videoId, String channelName, String channelId, 
                          Long subscribersCount, Long totalVideos, Long totalComments, 
                          Long totalLikes) {
        this.videoId = videoId;
        this.channelName = channelName;
        this.channelId = channelId;
        this.subscribersCount = subscribersCount;
        this.totalVideos = totalVideos;
        this.totalComments = totalComments;
        this.totalLikes = totalLikes;
        this.lastUpdated = LocalDateTime.now();
    }

    // Getters and Setters
    public String getVideoId() {
        return videoId;
    }

    public void setVideoId(String videoId) {
        this.videoId = videoId;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public Long getSubscribersCount() {
        return subscribersCount;
    }

    public void setSubscribersCount(Long subscribersCount) {
        this.subscribersCount = subscribersCount;
    }

    public Long getTotalVideos() {
        return totalVideos;
    }

    public void setTotalVideos(Long totalVideos) {
        this.totalVideos = totalVideos;
    }

    public Long getTotalComments() {
        return totalComments;
    }

    public void setTotalComments(Long totalComments) {
        this.totalComments = totalComments;
    }

    public Long getTotalLikes() {
        return totalLikes;
    }

    public void setTotalLikes(Long totalLikes) {
        this.totalLikes = totalLikes;
    }

    public LocalDateTime getLastUpdated() {
        return lastUpdated;
    }

    public void setLastUpdated(LocalDateTime lastUpdated) {
        this.lastUpdated = lastUpdated;
    }

    // Utility methods for analytics
    public double getEngagementRate() {
        if (subscribersCount == null || subscribersCount == 0) {
            return 0.0;
        }
        long totalEngagement = (totalLikes != null ? totalLikes : 0) + 
                              (totalComments != null ? totalComments : 0);
        return (double) totalEngagement / subscribersCount * 100;
    }

    public double getAverageLikesPerVideo() {
        if (totalVideos == null || totalVideos == 0) {
            return 0.0;
        }
        return totalLikes != null ? (double) totalLikes / totalVideos : 0.0;
    }

    @Override
    public String toString() {
        return "ChannelMetadata{" +
                "videoId='" + videoId + '\'' +
                ", channelName='" + channelName + '\'' +
                ", channelId='" + channelId + '\'' +
                ", subscribersCount=" + subscribersCount +
                ", totalVideos=" + totalVideos +
                ", totalComments=" + totalComments +
                ", totalLikes=" + totalLikes +
                ", lastUpdated=" + lastUpdated +
                '}';
    }
} 