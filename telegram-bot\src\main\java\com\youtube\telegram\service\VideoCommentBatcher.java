package com.youtube.telegram.service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.youtube.shared.model.CommentData;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

@Service
public class VideoCommentBatcher {

    private static final Logger logger = LoggerFactory.getLogger(VideoCommentBatcher.class);
    
    // Batch comments for 15 seconds per video before sending
    private static final int BATCH_DELAY_SECONDS = 15;
    
    private final YouTubeAnalyticsBot youTubeAnalyticsBot;
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor(); // Single thread to prevent race conditions
    
    // Map to store comments by video URL
    private final Map<String, VideoCommentBatch> videoBatches = new ConcurrentHashMap<>();
    private int videoCounter = 0; // Track video processing order
    
    public VideoCommentBatcher(YouTubeAnalyticsBot youTubeAnalyticsBot) {
        this.youTubeAnalyticsBot = youTubeAnalyticsBot;
    }
    
    @PostConstruct
    public void init() {
        logger.info("Video Comment Batcher initialized");
    }
    
    @PreDestroy
    public void cleanup() {
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * Add a comment to the batch for its video
     */
    public synchronized void addComment(CommentData commentData) {
        String videoUrl = commentData.getVideoUrl();
        String commentText = commentData.getCommentText();
        
        logger.debug("Adding comment ({} chars) to batch for video: {}", 
                    commentData.getCommentLength(), commentData.getVideoTitle());
        
        // Check if batch already exists
        VideoCommentBatch existingBatch = videoBatches.get(videoUrl);
        
        if (existingBatch != null) {
            // Check for duplicate comment
            if (existingBatch.hasComment(commentText)) {
                logger.warn("Skipping duplicate comment for video '{}': {} chars", 
                           commentData.getVideoTitle(), commentData.getCommentLength());
                return;
            }
            
            // Add to existing batch
            existingBatch.addComment(commentData);
            logger.debug("Added comment to existing batch for video: {} (now {} comments)", 
                        commentData.getVideoTitle(), existingBatch.getCommentCount());
        } else {
            // Create new batch
            VideoCommentBatch newBatch = new VideoCommentBatch(commentData);
            newBatch.setVideoNumber(++videoCounter); // Assign video number
            videoBatches.put(videoUrl, newBatch);
            
            // Schedule batch to be sent after delay
            scheduler.schedule(() -> {
                sendBatchedVideo(videoUrl);
            }, BATCH_DELAY_SECONDS, TimeUnit.SECONDS);
            
            logger.info("Created new batch for video #{}: '{}' (will send in {} seconds)", 
                       newBatch.getVideoNumber(), commentData.getVideoTitle(), BATCH_DELAY_SECONDS);
        }
    }
    
    /**
     * Send all comments for a video as a grouped message
     */
    private synchronized void sendBatchedVideo(String videoUrl) {
        VideoCommentBatch batch = videoBatches.remove(videoUrl);
        if (batch == null || batch.comments.isEmpty()) {
            return;
        }
        
        logger.info("Sending batched video #{} with {} comments: {}", 
                   batch.getVideoNumber(), batch.comments.size(), batch.videoTitle);
        
        try {
            // Send video header with thumbnail (use first comment and total count)
            CommentData firstComment = batch.comments.get(0);
            youTubeAnalyticsBot.sendVideoHeader(firstComment, batch.comments.size());
            
            // Small delay to ensure header is sent first
            Thread.sleep(500);
            
            // Send all comments for this video sequentially
            for (CommentData comment : batch.comments) {
                youTubeAnalyticsBot.sendVideoComment(comment, batch.comments.size());
                Thread.sleep(200); // Small delay between comments
            }
            
            // Send completion message
            youTubeAnalyticsBot.sendVideoCompletion(batch);
            
            logger.info("Completed sending video #{} with {} comments", 
                       batch.getVideoNumber(), batch.comments.size());
                       
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.error("Interrupted while sending video batch: {}", batch.videoTitle);
        } catch (Exception e) {
            logger.error("Error sending video batch: {}", batch.videoTitle, e);
        }
    }
    
    /**
     * Force send all pending batches (for testing or shutdown)
     */
    public void flushAllBatches() {
        logger.info("Flushing {} pending video batches", videoBatches.size());
        for (String videoUrl : new ArrayList<>(videoBatches.keySet())) {
            sendBatchedVideo(videoUrl);
        }
    }
    
    /**
     * Container for batched comments from one video
     */
    public static class VideoCommentBatch {
        private final String videoUrl;
        private final String videoTitle;
        private final String videoThumbnail;
        private final List<CommentData> comments = new ArrayList<>();
        private final LocalDateTime firstCommentTime;
        private int videoNumber;
        
        public VideoCommentBatch(CommentData firstComment) {
            this.videoUrl = firstComment.getVideoUrl();
            this.videoTitle = firstComment.getVideoTitle();
            this.videoThumbnail = firstComment.getVideoThumbnail();
            this.firstCommentTime = LocalDateTime.now();
            this.comments.add(firstComment);
        }
        
        public void addComment(CommentData comment) {
            this.comments.add(comment);
        }
        
        // Getters
        public String getVideoUrl() { return videoUrl; }
        public String getVideoTitle() { return videoTitle; }
        public String getVideoThumbnail() { return videoThumbnail; }
        public List<CommentData> getComments() { return comments; }
        public LocalDateTime getFirstCommentTime() { return firstCommentTime; }
        public int getCommentCount() { return comments.size(); }
        public int getVideoNumber() { return videoNumber; }
        public void setVideoNumber(int videoNumber) { this.videoNumber = videoNumber; }
        
        public boolean hasComment(String commentText) {
            for (CommentData comment : comments) {
                if (comment.getCommentText().equals(commentText)) {
                    return true;
                }
            }
            return false;
        }
    }
} 