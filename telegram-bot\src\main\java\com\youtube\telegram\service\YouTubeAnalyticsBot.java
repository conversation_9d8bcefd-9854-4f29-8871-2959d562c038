package com.youtube.telegram.service;

import java.time.format.DateTimeFormatter;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.telegram.telegrambots.bots.TelegramLongPollingBot;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import org.telegram.telegrambots.meta.api.methods.send.SendPhoto;
import org.telegram.telegrambots.meta.api.objects.InputFile;
import org.telegram.telegrambots.meta.api.objects.Update;
import org.telegram.telegrambots.meta.exceptions.TelegramApiException;

import com.youtube.shared.model.CommentData;
import com.youtube.telegram.entity.TelegramSubscriber;
import com.youtube.telegram.repository.TelegramSubscriberRepository;

@Component
public class YouTubeAnalyticsBot extends TelegramLongPollingBot {

    private static final Logger logger = LoggerFactory.getLogger(YouTubeAnalyticsBot.class);
    
    private final String botToken;
    private final String botUsername;
    private final TelegramSubscriberRepository subscriberRepository;
    private final WebClient webClient;
    private VideoCommentBatcher videoCommentBatcher; // Will be injected after construction
    private KafkaOffsetResetService kafkaOffsetResetService; // Will be injected after construction

    public YouTubeAnalyticsBot(@Value("${telegram.bot.token}") String botToken,
                              @Value("${telegram.bot.username}") String botUsername,
                              TelegramSubscriberRepository subscriberRepository) {
        this.botToken = botToken;
        this.botUsername = botUsername;
        this.subscriberRepository = subscriberRepository;
        this.webClient = WebClient.builder().build();
        logger.info("Telegram bot initialized: {}", botUsername);

        // Log existing subscribers on startup
        long existingSubscribers = subscriberRepository.countActiveSubscribers();
        logger.info("Found {} existing subscribers in database", existingSubscribers);
    }

    // Setter for VideoCommentBatcher (to avoid circular dependency)
    public void setVideoCommentBatcher(VideoCommentBatcher videoCommentBatcher) {
        this.videoCommentBatcher = videoCommentBatcher;
    }

    // Setter for KafkaOffsetResetService
    public void setKafkaOffsetResetService(KafkaOffsetResetService kafkaOffsetResetService) {
        this.kafkaOffsetResetService = kafkaOffsetResetService;
    }

    @Override
    public String getBotToken() {
        return botToken;
    }

    @Override
    public String getBotUsername() {
        return botUsername;
    }

    @Override
    public void onUpdateReceived(Update update) {
        if (update.hasMessage() && update.getMessage().hasText()) {
            String messageText = update.getMessage().getText();
            String chatId = update.getMessage().getChatId().toString();
            String userName = update.getMessage().getFrom().getUserName();
            String firstName = update.getMessage().getFrom().getFirstName();
            String lastName = update.getMessage().getFrom().getLastName();
            
            logger.info("Received message from {}: {}", firstName != null ? firstName : userName, messageText);
            
            switch (messageText.toLowerCase()) {
                case "/start":
                    handleStartCommand(chatId, userName, firstName, lastName);
                    break;
                case "/subscribe":
                    handleSubscribeCommand(chatId, userName, firstName, lastName);
                    break;
                case "/activate":
                    handleActivateCommand(chatId, firstName != null ? firstName : userName);
                    break;
                case "/unsubscribe":
                    handleUnsubscribeCommand(chatId, firstName != null ? firstName : userName);
                    break;
                case "/status":
                    handleStatusCommand(chatId);
                    break;
                case "/help":
                    handleHelpCommand(chatId);
                    break;
                default:
                    handleUnknownCommand(chatId);
            }
        }
    }

    private void handleStartCommand(String chatId, String userName, String firstName, String lastName) {
        String displayName = firstName != null ? firstName : (userName != null ? userName : "User");
        
        String welcomeMessage = String.format(
            "🤖 *Welcome to YouTube Analytics Bot\\!*\n\n" +
            "Hello %s\\! I'm here to help you track YouTube video comments in real\\-time\\.\n\n" +
            "🔔 *Next step:* Use `/subscribe` to receive notifications\\!\n\n" +
            "💡 *Quick tip:* Use `/help` to see all available commands\\.",
            escapeMarkdown(displayName)
        );
        sendMessage(chatId, welcomeMessage, true);
        logger.info("Sent welcome message to {} ({})", displayName, chatId);
    }

    private void handleSubscribeCommand(String chatId, String userName, String firstName, String lastName) {
        try {
            // Check if already subscribed
            if (subscriberRepository.existsByChatIdAndIsActiveTrue(chatId)) {
                String displayName = firstName != null ? firstName : (userName != null ? userName : "User");
                sendMessage(chatId, 
                    String.format("ℹ️ You're already subscribed, %s\\! Use `/activate` to start receiving data\\.", 
                                 escapeMarkdown(displayName)), 
                    true);
                return;
            }
            
            // Create new subscriber
            TelegramSubscriber subscriber = new TelegramSubscriber(chatId, userName, firstName, lastName);
            subscriberRepository.save(subscriber);
            
            String displayName = subscriber.getDisplayName();
            String subscribeMessage = String.format(
                "✅ *Successfully subscribed\\!*\n\n" +
                "Welcome %s\\! You will now receive YouTube comment notifications\\.\n\n" +
                "🚀 *Next step:* Use `/activate` to begin real\\-time data flow\\!\n\n" +
                "📱 *Available commands:*\n" +
                "• `/activate` \\- Start receiving data\n" +
                "• `/unsubscribe` \\- Stop notifications\n" +
                "• `/status` \\- Check subscription status\n" +
                "• `/help` \\- View all commands",
                escapeMarkdown(displayName)
            );
            sendMessage(chatId, subscribeMessage, true);
            logger.info("New subscriber added: {} ({})", displayName, chatId);
            
        } catch (Exception e) {
            logger.error("Error subscribing user {}: {}", chatId, e.getMessage());
            sendMessage(chatId, "❌ Error subscribing. Please try again later.", false);
        }
    }

    private void handleActivateCommand(String chatId, String displayName) {
        if (!subscriberRepository.existsByChatIdAndIsActiveTrue(chatId)) {
            sendMessage(chatId, 
                "⚠️ *Please subscribe first\\!*\n\n" +
                "Use `/subscribe` to start receiving notifications, then use `/activate` to begin data flow\\.", 
                true);
            return;
        }
        
        // Update last activity
        subscriberRepository.findByChatId(chatId).ifPresent(subscriber -> {
            subscriber.setLastActivity(java.time.LocalDateTime.now());
            subscriberRepository.save(subscriber);
        });
        
        String activateMessage = String.format(
            "🚀 *Data Flow Activated\\!*\n\n" +
            "%s, your bot is now actively monitoring for new YouTube data\\.\n\n" +
            "📋 *What happens next:*\n" +
            "• Submit 5 YouTube URLs in Producer App \\(localhost:8081\\)\n" +
            "• Bot will automatically receive odd\\-length comments\n" +
            "• Real\\-time notifications will appear here\n" +
            "• Check analytics at localhost:8083\n\n" +
            "⏳ *Waiting for data\\.\\.\\.*",
            escapeMarkdown(displayName)
        );
        sendMessage(chatId, activateMessage, true);
        logger.info("User {} activated data flow", displayName);
    }

    private void handleUnsubscribeCommand(String chatId, String displayName) {
        try {
            subscriberRepository.findByChatId(chatId).ifPresentOrElse(
                subscriber -> {
                    subscriber.setIsActive(false);
                    subscriberRepository.save(subscriber);
                    
            String message = String.format(
                "❌ *Unsubscribed successfully!*\n\n" +
                "%s, you will no longer receive YouTube comment notifications.",
                        displayName
            );
            sendMessage(chatId, message, true);
                    logger.info("User {} unsubscribed from notifications", displayName);
                },
                () -> {
            sendMessage(chatId, "ℹ️ You are not currently subscribed to notifications.", true);
                }
            );
        } catch (Exception e) {
            logger.error("Error unsubscribing user {}: {}", chatId, e.getMessage());
            sendMessage(chatId, "❌ Error unsubscribing. Please try again later.", false);
        }
    }

    private void handleStatusCommand(String chatId) {
        try {
            boolean isSubscribed = subscriberRepository.existsByChatIdAndIsActiveTrue(chatId);
            long totalSubscribers = subscriberRepository.countActiveSubscribers();
        
        String statusMessage = String.format(
            "📊 *Bot Status*\n\n" +
            "Your subscription: %s\n" +
            "Total subscribers: %d\n" +
                "Bot status: 🟢 Active\n" +
                "Database: 🟢 Connected",
            isSubscribed ? "✅ Subscribed" : "❌ Not subscribed",
            totalSubscribers
        );
        sendMessage(chatId, statusMessage, true);
        } catch (Exception e) {
            logger.error("Error getting status for user {}: {}", chatId, e.getMessage());
            sendMessage(chatId, "❌ Error getting status. Database might be unavailable.", false);
        }
    }

    private void handleHelpCommand(String chatId) {
        String helpMessage = 
            "🆘 *Available Commands:*\n\n" +
            "• `/start` \\- Welcome message and introduction\n" +
            "• `/subscribe` \\- Subscribe to comment notifications\n" +
            "• `/activate` \\- Activate real\\-time data processing\n" +
            "• `/unsubscribe` \\- Unsubscribe from notifications\n" +
            "• `/status` \\- Check your subscription status\n" +
            "• `/help` \\- Show this help message\n\n" +
            "ℹ️ *About this bot:*\n" +
            "This bot receives YouTube comments with odd character lengths from our analytics system " +
            "and forwards them to subscribed users in real\\-time\\.\n\n" +
            "🎯 *Workflow:*\n" +
            "1\\. Submit 5 YouTube URLs in Producer App\n" +
            "2\\. Subscribe to this bot using `/subscribe`\n" +
            "3\\. Activate data flow using `/activate`\n" +
            "4\\. Receive odd\\-length comments automatically\\!\n\n" +
            "✨ *Note:* Your subscription persists across bot restarts\\!";
        sendMessage(chatId, helpMessage, true);
    }

    private void handleUnknownCommand(String chatId) {
        sendMessage(chatId, "❓ Unknown command. Use /help to see available commands.", false);
    }

    /**
     * Notify subscribers about new processing session
     */
    public void notifyProcessingStarted(String processingSession, int videoCount) {
        List<TelegramSubscriber> activeSubscribers = subscriberRepository.findByIsActiveTrue();
        
        if (activeSubscribers.isEmpty()) {
            logger.debug("No subscribers to notify about processing start");
            return;
        }
        
        String notification = String.format(
            "🎬 *New YouTube Processing Started\\!*\n\n" +
            "📊 *Session:* `%s`\n" +
            "📹 *Videos:* %d YouTube links submitted\n" +
            "🔄 *Status:* Processing comments\\.\\.\\.\n\n" +
            "⏳ *Odd\\-length comments will appear here soon\\!*",
            processingSession.substring(0, 8) + "...", // Show first 8 chars
            videoCount
        );
        
        for (TelegramSubscriber subscriber : activeSubscribers) {
            sendMessage(subscriber.getChatId(), notification, true);
        }
        
        logger.info("Notified {} subscribers about processing start", activeSubscribers.size());
    }

    /**
     * Send a YouTube comment to all subscribed users
     */
    public void broadcastComment(CommentData commentData) {
        List<TelegramSubscriber> activeSubscribers = subscriberRepository.findByIsActiveTrue();
        
        if (activeSubscribers.isEmpty()) {
            logger.debug("No subscribers to broadcast comment");
            return;
        }
        
        // Send video thumbnail first (if available)
        if (commentData.getVideoThumbnail() != null && !commentData.getVideoThumbnail().isEmpty()) {
            for (TelegramSubscriber subscriber : activeSubscribers) {
                try {
                    sendPhoto(subscriber.getChatId(), commentData.getVideoThumbnail(), formatCommentCaption(commentData));
                } catch (Exception e) {
                    logger.error("Failed to send photo to chat {}: {}", subscriber.getChatId(), e.getMessage());
                    // Fallback to text message
                    sendMessage(subscriber.getChatId(), formatCommentMessage(commentData), true);
                }
            }
        } else {
            // Send text message if no thumbnail
            String formattedMessage = formatCommentMessage(commentData);
            
            for (TelegramSubscriber subscriber : activeSubscribers) {
                try {
                    sendMessage(subscriber.getChatId(), formattedMessage, true);
                } catch (Exception e) {
                    logger.error("Failed to send comment to chat {}: {}", subscriber.getChatId(), e.getMessage());
                    // Mark subscriber as inactive if consistent failures
                    markSubscriberInactive(subscriber.getChatId());
                }
            }
        }
        
        logger.info("Broadcasted comment to {} subscribers", activeSubscribers.size());
    }

    /**
     * Mark subscriber as inactive due to delivery failures
     */
    private void markSubscriberInactive(String chatId) {
        try {
            subscriberRepository.findByChatId(chatId).ifPresent(subscriber -> {
                subscriber.setIsActive(false);
                subscriberRepository.save(subscriber);
                logger.warn("Marked subscriber {} as inactive due to delivery failures", chatId);
            });
        } catch (Exception e) {
            logger.error("Error marking subscriber {} as inactive: {}", chatId, e.getMessage());
        }
    }

    /**
     * Format comment for display with thumbnail caption
     */
    private String formatCommentCaption(CommentData commentData) {
        return String.format(
            "💬 %s\n👤 %s • 👍 %d\n📏 %d chars (odd)",
            escapeMarkdown(commentData.getCommentText()),
            escapeMarkdown(commentData.getAuthorName() != null ? commentData.getAuthorName() : "Unknown"),
            commentData.getLikesCount() != null ? commentData.getLikesCount() : 0,
            commentData.getCommentLength()
        );
    }

    /**
     * Format comment for text message display
     */
    private String formatCommentMessage(CommentData commentData) {
        return String.format(
            "🎥 *%s*\n\n" +
            "💬 _%s_\n\n" +
            "👤 *Author:* %s\n" +
            "👍 *Likes:* %d\n" +
            "📏 *Length:* %d characters \\(odd\\)\n" +
            "🔗 [Watch Video](%s)",
            escapeMarkdown(commentData.getVideoTitle() != null ? commentData.getVideoTitle() : "YouTube Video"),
            escapeMarkdown(commentData.getCommentText()),
            escapeMarkdown(commentData.getAuthorName() != null ? commentData.getAuthorName() : "Unknown"),
            commentData.getLikesCount() != null ? commentData.getLikesCount() : 0,
            commentData.getCommentLength(),
            commentData.getVideoUrl()
        );
    }

    /**
     * Escape markdown special characters
     */
    private String escapeMarkdown(String text) {
        if (text == null) return "";
        return text.replaceAll("([_*\\[\\]()~`>#+\\-=|{}.!\\\\])", "\\\\$1");
    }

    public void sendMessage(String chatId, String text, boolean markdown) {
        SendMessage message = new SendMessage();
        message.setChatId(chatId);
        message.setText(text);
        
        if (markdown) {
            message.setParseMode("MarkdownV2");
        }
        
        try {
            execute(message);
        } catch (TelegramApiException e) {
            logger.error("Failed to send message to chat {}: {}", chatId, e.getMessage());
        }
    }

    private void sendPhoto(String chatId, String photoUrl, String caption) {
        SendPhoto photoMessage = new SendPhoto();
        photoMessage.setChatId(chatId);
        photoMessage.setPhoto(new InputFile(photoUrl));
        photoMessage.setCaption(caption);
        
        try {
            execute(photoMessage);
        } catch (TelegramApiException e) {
            logger.error("Failed to send photo to chat {}: {}", chatId, e.getMessage());
        }
    }

    /**
     * Get subscription statistics
     */
    public BotStats getStats() {
        long subscribedUsers = subscriberRepository.countActiveSubscribers();
        return new BotStats((int) subscribedUsers, subscribedUsers > 0);
    }

    /**
     * Bot statistics data class
     */
    public static class BotStats {
        private final int subscribedUsers;
        private final boolean hasSubscribers;

        public BotStats(int subscribedUsers, boolean hasSubscribers) {
            this.subscribedUsers = subscribedUsers;
            this.hasSubscribers = hasSubscribers;
        }

        public int getSubscribedUsers() { return subscribedUsers; }
        public boolean isHasSubscribers() { return hasSubscribers; }
    }

    /**
     * Send video header with thumbnail and title
     */
    public void sendVideoHeader(CommentData firstComment, int totalComments) {
        List<TelegramSubscriber> activeSubscribers = subscriberRepository.findByIsActiveTrue();
        
        if (activeSubscribers.isEmpty()) {
            logger.debug("No subscribers to send video header");
            return;
        }
        
        resetVideoCommentCounter(); // Reset for new video
        
        String headerMessage = String.format(
            "🎬 *New Video Analysis\\!*\n\n" +
            "📹 %s\n" +
            "🔗 [Watch Video](%s)\n\n" +
            "📊 Found %d odd\\-length comments\\!\n" +
            "⏳ *Sending comments\\.\\.\\.*",
            escapeMarkdown(firstComment.getVideoTitle() != null ? firstComment.getVideoTitle() : "YouTube Video"),
            firstComment.getVideoUrl(),
            totalComments
        );
        
        for (TelegramSubscriber subscriber : activeSubscribers) {
            sendMessage(subscriber.getChatId(), headerMessage, true);
        }
        
        logger.info("Sent video header to {} subscribers", activeSubscribers.size());
    }
    
    // Video comment counter (for displaying comment numbers)
    private int videoCommentCounter = 0;
    private int videoCounter = 0;

    public void resetCounters() {
        videoCounter = 0;
        videoCommentCounter = 0;
        logger.debug("Reset video and comment counters");
    }

    public void resetVideoCommentCounter() {
        videoCommentCounter = 0;
    }

    private int getCurrentVideoCommentNumber() {
        return ++videoCommentCounter;
    }

    public void sendVideoComment(CommentData commentData, int totalComments) {
        List<TelegramSubscriber> activeSubscribers = subscriberRepository.findByIsActiveTrue();
        
        if (activeSubscribers.isEmpty()) {
            logger.debug("No subscribers to send video comment");
            return;
        }
        
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        
        // Use safer message format to avoid markdown parsing issues
        String commentMessage = String.format(
            "💭 *Comment %d:*\n" +
            "👤 %s • 👍 %d likes\n" +
            "%s\n" +
            "📏 %d characters \\(odd\\)",
            getCurrentVideoCommentNumber(), // Use per-video counter
            escapeMarkdown(commentData.getAuthorName() != null ? commentData.getAuthorName() : "Unknown"),
            commentData.getLikesCount() != null ? commentData.getLikesCount() : 0,
            escapeMarkdown(commentData.getCommentText()), // Remove italic formatting
            commentData.getCommentLength()
        );
        
        for (TelegramSubscriber subscriber : activeSubscribers) {
            sendMessage(subscriber.getChatId(), commentMessage, true);
        }
    }
    
    /**
     * Send video completion message
     */
    public void sendVideoCompletion(VideoCommentBatcher.VideoCommentBatch batch) {
        List<TelegramSubscriber> activeSubscribers = subscriberRepository.findByIsActiveTrue();
        
        if (activeSubscribers.isEmpty()) {
            logger.debug("No subscribers to send video completion");
            return;
        }
        
        String completionMessage = String.format(
            "✅ *Video Complete\\!*\n\n" +
            "📊 Total odd\\-length comments: %d\n" +
            "⏰ Processing time: %s\n\n" +
            "🔄 *Moving to next video\\.\\.\\.*",
            batch.getCommentCount(),
            "~15 seconds"
        );
        
        for (TelegramSubscriber subscriber : activeSubscribers) {
            sendMessage(subscriber.getChatId(), completionMessage, true);
        }
        
        logger.info("Sent completion message to {} subscribers", activeSubscribers.size());
    }
    
    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        logger.info("Application ready - performing fresh start reset...");

        try {
            // STEP 1: Reset Kafka consumer offsets for fresh start
            if (kafkaOffsetResetService != null) {
                kafkaOffsetResetService.resetTelegramConsumerOffsets();
                logger.info("Reset Kafka consumer offsets for fresh start");
            }

            // STEP 2: Clear any previous video batches on startup (fresh start)
            if (videoCommentBatcher != null) {
                videoCommentBatcher.clearAll();
                logger.info("Cleared previous video batches on startup for fresh start");
            }

            // STEP 3: Reset counters
            resetCounters();

            List<TelegramSubscriber> activeSubscribers = subscriberRepository.findByIsActiveTrue();

            if (activeSubscribers.isEmpty()) {
                logger.info("No active subscribers found for startup notification");
                return;
            }

            logger.info("Found {} active subscribers, checking for existing video data...", activeSubscribers.size());

            // Check consumer app for existing active videos
            sendStartupVideoData(activeSubscribers);

        } catch (Exception e) {
            logger.error("Error during startup notification: {}", e.getMessage());
    }
    }

    /**
     * Send existing active video data once on startup
     */
    private void sendStartupVideoData(List<TelegramSubscriber> subscribers) {
        try {
            // Query consumer app for existing video data
            String response = webClient.get()
                .uri("http://youtube-consumer:8082/status")
                .retrieve()
                .bodyToMono(String.class)
                .block();
            
            if (response == null || !response.contains("videos")) {
                sendWelcomeBackMessage(subscribers, false);
                return;
            }

            // Send welcome message with existing data info
            sendWelcomeBackMessage(subscribers, true);
            
            logger.info("Sent startup notification to {} subscribers about existing video data", subscribers.size());

        } catch (Exception e) {
            logger.warn("Could not check consumer app for existing data: {}", e.getMessage());
            sendWelcomeBackMessage(subscribers, false);
        }
    }
    
    /**
     * Send welcome back message to existing subscribers
     */
    private void sendWelcomeBackMessage(List<TelegramSubscriber> subscribers, boolean hasExistingData) {
        String welcomeBackMessage;
        
        if (hasExistingData) {
            welcomeBackMessage = 
                "🔄 *Bot Restarted - Welcome Back\\!*\n\n" +
                "✅ Your subscription is active\\!\n" +
                "📊 *Found existing video analysis data\\!*\n\n" +
                "🎥 There are currently active videos being monitored\\.\n" +
                "💬 You'll receive NEW comments as they come in\\.\n\n" +
                "🆕 Submit videos: localhost:8081\n" +
                "📈 View analytics: localhost:8083\n\n" +
                "⏳ *Monitoring for new activity\\.\\.\\.*";
        } else {
            welcomeBackMessage = 
                "🔄 *Bot Restarted - Welcome Back\\!*\n\n" +
                "✅ Your subscription is active\\!\n" +
                "🔍 No active videos found\\.\n\n" +
                "🎯 *Ready for new submissions\\!*\n" +
                "🆕 Submit videos: localhost:8081\n" +
                "📈 View analytics: localhost:8083\n\n" +
                "⏳ *Waiting for new data\\.\\.\\.*";
        }

        for (TelegramSubscriber subscriber : subscribers) {
            sendMessage(subscriber.getChatId(), welcomeBackMessage, true);
        }
    }
} 