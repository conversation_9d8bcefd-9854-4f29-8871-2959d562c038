# 📊 Real-Time YouTube Data Analysis with <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and Spring Boot Web Applications

## 🎯 Project Overview

This project builds a **real-time distributed application** that fetches YouTube video data, processes top-liked comments, and sends selective results to a **Telegram bot** or a **consumer web dashboard**. It leverages:

- **Apache Kafka** for streaming data
- **Spring Boot** for producer/consumer apps
- **YouTube Data API** to retrieve video info and comments
- **Telegram Bot** for comment notifications
- **MySQL** for link storage
- **Docker** for orchestration

---

## 🔄 System Flow

### ✅ 1. Spring Boot Producer Web App

- User submits **5 YouTube video links** through the web interface.
- Links are stored in a **MySQL database**.
- A **scheduled task** (every 30 seconds) performs the following:
  1. Fetch each video link from the database.
  2. Query the YouTube Data API for:
     - Channel Name  
     - Subscribers  
     - Total Videos  
     - Total Comments  
     - Total Likes  
     - 🔥 **Top 5 most liked comments only**
  3. Package the data and send it to **Apache Kafka**.

---

### 🧠 2. Kafka Processing Logic

Kafka receives the data and performs the following logic on each of the **top 5 liked comments** per video:

- If a **comment has odd character length**:
  - Send the following to **Telegram <PERSON>**:
    ```
    📹 YouTube Link: <video link>
    📊 Channel Info:
    - Channel Name
    - Subscribers
    - Total Videos
    - Total Comments
    - Total Likes
    💬 Comment: <odd-length comment text>
    ```

- If a **comment has even character length**:
  - Send **channel metadata** to the **consumer app**.
  - **Only the first even-length comment per video** will trigger this; additional even comments are ignored.

---

### 📊 3. Consumer Web App

- Shows analytics only for videos with at least **one even-length comment**.
- Displays comparisons such as:
  - Channel with **most subscribers**
  - Channel with **most videos**
  - Channel with **most comments**
  - Channel with **most likes**
  - Channel with **highest average views**
  - Channel with **best engagement rate**

---

### 📲 4. Telegram Bot

- Displays **odd-length comments** in real-time.
- Each message includes:
  - YouTube link
  - Channel metadata
  - The comment text

✔️ Each message is complete and sent individually.

---

## 🔁 Real-Time Update Logic

- The producer periodically polls the YouTube API.
- If a new comment is added to a video:
  - It will be **picked up during the next scheduled fetch**.
  - Kafka processes it and updates the respective output (Telegram/Consumer App).
- ⚠️ This real-time behavior is **polling-based**, not push-based.

---

## 🧩 Technical Stack

| Component       | Technology Used        |
|----------------|------------------------|
| UI Input       | Spring Boot + Thymeleaf |
| Backend Logic  | Spring Boot            |
| Messaging      | Apache Kafka           |
| Bot Messaging  | Telegram Bot API       |
| Data Source    | YouTube Data API       |
| Database       | MySQL                  |
| Deployment     | Docker + Docker Compose|

---


