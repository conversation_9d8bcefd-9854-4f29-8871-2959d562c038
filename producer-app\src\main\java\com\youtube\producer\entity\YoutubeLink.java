package com.youtube.producer.entity;

import java.time.LocalDateTime;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

@Entity
@Table(name = "youtube_links")
public class YoutubeLink {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "video_url", nullable = false, unique = true)
    @NotBlank(message = "Video URL cannot be blank")
    @Pattern(regexp = "^https?://(www\\.)?(youtube\\.com/watch\\?v=|youtu\\.be/)[a-zA-Z0-9_-]+.*$", 
             message = "Invalid YouTube URL format")
    private String videoUrl;

    @Column(name = "video_id", nullable = false, length = 50)
    private String videoId;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "is_active")
    private Boolean isActive = true;

    @Column(name = "processing_session", length = 100)
    private String processingSession;

    @Column(name = "metadata_sent")
    private Boolean metadataSent = false;

    @Column(name = "processed_for_telegram")
    private Boolean processedForTelegram = false;

    // Constructors
    public YoutubeLink() {}

    public YoutubeLink(String videoUrl, String videoId) {
        this.videoUrl = videoUrl;
        this.videoId = videoId;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        this.isActive = true;
        this.metadataSent = false;
        this.processedForTelegram = false;
    }

    // JPA lifecycle methods
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
        // Extract video ID from URL
        this.videoId = extractVideoId(videoUrl);
    }

    public String getVideoId() {
        return videoId;
    }

    public void setVideoId(String videoId) {
        this.videoId = videoId;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public String getProcessingSession() {
        return processingSession;
    }

    public void setProcessingSession(String processingSession) {
        this.processingSession = processingSession;
    }

    public Boolean getMetadataSent() {
        return metadataSent;
    }

    public void setMetadataSent(Boolean metadataSent) {
        this.metadataSent = metadataSent;
    }

    public Boolean getProcessedForTelegram() {
        return processedForTelegram;
    }

    public void setProcessedForTelegram(Boolean processedForTelegram) {
        this.processedForTelegram = processedForTelegram;
    }

    // Utility method to extract video ID from YouTube URL
    private String extractVideoId(String url) {
        if (url == null || url.isEmpty()) {
            return null;
        }
        
        String videoId = null;
        
        // Handle youtube.com/watch?v= format
        if (url.contains("youtube.com/watch?v=")) {
            int startIndex = url.indexOf("v=") + 2;
            int endIndex = url.indexOf("&", startIndex);
            if (endIndex == -1) {
                endIndex = url.length();
            }
            videoId = url.substring(startIndex, endIndex);
        }
        // Handle youtu.be/ format
        else if (url.contains("youtu.be/")) {
            int startIndex = url.indexOf("youtu.be/") + 9;
            int endIndex = url.indexOf("?", startIndex);
            if (endIndex == -1) {
                endIndex = url.length();
            }
            videoId = url.substring(startIndex, endIndex);
        }
        
        return videoId;
    }

    @Override
    public String toString() {
        return "YoutubeLink{" +
                "id=" + id +
                ", videoUrl='" + videoUrl + '\'' +
                ", videoId='" + videoId + '\'' +
                ", isActive=" + isActive +
                ", processingSession='" + processingSession + '\'' +
                ", metadataSent=" + metadataSent +
                ", processedForTelegram=" + processedForTelegram +
                '}';
    }
} 