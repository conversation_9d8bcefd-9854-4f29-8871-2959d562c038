package com.youtube.telegram.service;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

@Service
public class ControlMessageConsumerService {

    private static final Logger logger = LoggerFactory.getLogger(ControlMessageConsumerService.class);

    private final YouTubeAnalyticsBot youTubeAnalyticsBot;
    private final VideoCommentBatcher videoCommentBatcher;

    public ControlMessageConsumerService(YouTubeAnalyticsBot youTubeAnalyticsBot,
                                       VideoCommentBatcher videoCommentBatcher) {
        this.youTubeAnalyticsBot = youTubeAnalyticsBot;
        this.videoCommentBatcher = videoCommentBatcher;
    }

    /**
     * Listen for control messages from producer app
     */
    @KafkaListener(topics = "youtube-control", groupId = "youtube-telegram-control-group-v3")
    public void handleControlMessage(Map<String, String> message) {
        logger.info("Received control message: {}", message);

        try {
            String action = message.get("action");
            String processingSession = message.get("processingSession");
            
            if ("clear".equals(action)) {
                logger.info("New processing session started: {}. Clearing all previous data and notifying Telegram subscribers.", processingSession);

                // Clear all video batches and reset counters
                videoCommentBatcher.clearAll();

                // Reset bot counters for new session
                youTubeAnalyticsBot.resetCounters();

                // Notify subscribers about new processing
                youTubeAnalyticsBot.notifyProcessingStarted(processingSession, 5); // 5 videos expected

                logger.info("Successfully cleared previous data and notified subscribers about new processing session: {}", processingSession);
            } else {
                logger.warn("Unknown control action received: {}", action);
            }

        } catch (Exception e) {
            logger.error("Error processing control message: {}", message, e);
        }
    }
} 