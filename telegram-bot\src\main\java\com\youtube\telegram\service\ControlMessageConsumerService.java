package com.youtube.telegram.service;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

@Service
public class ControlMessageConsumerService {

    private static final Logger logger = LoggerFactory.getLogger(ControlMessageConsumerService.class);

    private final YouTubeAnalyticsBot youTubeAnalyticsBot;

    public ControlMessageConsumerService(YouTubeAnalyticsBot youTubeAnalyticsBot) {
        this.youTubeAnalyticsBot = youTubeAnalyticsBot;
    }

    /**
     * Listen for control messages from producer app
     */
    @KafkaListener(topics = "youtube-control", groupId = "youtube-telegram-control-group")
    public void handleControlMessage(Map<String, String> message) {
        logger.info("Received control message: {}", message);

        try {
            String action = message.get("action");
            String processingSession = message.get("processingSession");
            
            if ("clear".equals(action)) {
                logger.info("New processing session started: {}. Notifying Telegram subscribers.", processingSession);
                
                // Reset video and comment counters for new session
                youTubeAnalyticsBot.resetCounters();
                
                // Notify subscribers about new processing
                youTubeAnalyticsBot.notifyProcessingStarted(processingSession, 5); // 5 videos expected
                
                logger.info("Successfully notified subscribers about new processing session: {}", processingSession);
            } else {
                logger.warn("Unknown control action received: {}", action);
            }

        } catch (Exception e) {
            logger.error("Error processing control message: {}", message, e);
        }
    }
} 