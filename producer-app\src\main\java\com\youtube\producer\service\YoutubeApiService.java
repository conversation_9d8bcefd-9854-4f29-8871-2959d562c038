package com.youtube.producer.service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.jackson2.JacksonFactory;
import com.google.api.services.youtube.YouTube;
import com.google.api.services.youtube.model.Channel;
import com.google.api.services.youtube.model.ChannelListResponse;
import com.google.api.services.youtube.model.ChannelStatistics;
import com.google.api.services.youtube.model.CommentSnippet;
import com.google.api.services.youtube.model.CommentThread;
import com.google.api.services.youtube.model.CommentThreadListResponse;
import com.google.api.services.youtube.model.Video;
import com.google.api.services.youtube.model.VideoListResponse;
import com.google.api.services.youtube.model.VideoSnippet;
import com.google.api.services.youtube.model.VideoStatistics;
import com.youtube.shared.model.ChannelMetadata;
import com.youtube.shared.model.CommentData;

@Service
public class YoutubeApiService {

    private static final Logger logger = LoggerFactory.getLogger(YoutubeApiService.class);
    
    private final YouTube youtube;
    private final String apiKey;

    public YoutubeApiService(@Value("${youtube.api.key}") String apiKey) {
        this.apiKey = apiKey;
        this.youtube = new YouTube.Builder(
            new NetHttpTransport(),
            JacksonFactory.getDefaultInstance(),
            null
        ).setApplicationName("YouTube Analytics System").build();
    }

    /**
     * Fetch complete video data including channel metadata and 5 newest comments
     * Returns null if video has fewer than 5 comments
     */
    public VideoData fetchVideoData(String videoId) {
        try {
            // Fetch video details
            VideoListResponse videoResponse = youtube.videos()
                .list(List.of("snippet", "statistics"))
                .setId(List.of(videoId))
                .setKey(apiKey)
                .execute();

            if (videoResponse.getItems().isEmpty()) {
                logger.warn("No video found for ID: {}", videoId);
                return null;
            }

            Video video = videoResponse.getItems().get(0);
            VideoSnippet snippet = video.getSnippet();
            VideoStatistics stats = video.getStatistics();

            // Check if video has at least 5 comments
            Long commentCount = stats.getCommentCount() != null ? stats.getCommentCount().longValue() : 0L;
            if (commentCount < 5) {
                logger.info("Video {} has only {} comments, skipping (need at least 5)", videoId, commentCount);
                return null;
            }

            // Fetch channel details
            ChannelListResponse channelResponse = youtube.channels()
                .list(List.of("snippet", "statistics"))
                .setId(List.of(snippet.getChannelId()))
                .setKey(apiKey)
                .execute();

            Channel channel = channelResponse.getItems().get(0);
            ChannelStatistics channelStats = channel.getStatistics();

            // Create channel metadata
            ChannelMetadata channelMetadata = new ChannelMetadata(
                videoId,
                snippet.getChannelTitle(),
                snippet.getChannelId(),
                channelStats.getSubscriberCount() != null ? channelStats.getSubscriberCount().longValue() : 0L,
                channelStats.getVideoCount() != null ? channelStats.getVideoCount().longValue() : 0L,
                commentCount,
                stats.getLikeCount() != null ? stats.getLikeCount().longValue() : 0L
            );

            // Get video title and thumbnail
            String videoTitle = snippet.getTitle();
            String videoThumbnail = snippet.getThumbnails().getMedium() != null ? 
                snippet.getThumbnails().getMedium().getUrl() : 
                snippet.getThumbnails().getDefault().getUrl();

            // Fetch 5 newest comments
            List<CommentData> newestComments = fetchNewestComments(videoId, videoTitle, videoThumbnail, channelMetadata, 5);
            
            if (newestComments.size() < 5) {
                logger.info("Could only fetch {} comments for video {}, skipping", newestComments.size(), videoId);
                return null;
            }

            return new VideoData(videoId, videoTitle, videoThumbnail, channelMetadata, newestComments);

        } catch (IOException e) {
            logger.error("Error fetching video data for ID: {}", videoId, e);
            return null;
        }
    }

    /**
     * Fetch N newest comments for a video
     */
    private List<CommentData> fetchNewestComments(String videoId, String videoTitle, String videoThumbnail, ChannelMetadata channelMetadata, int limit) {
        try {
            List<CommentData> comments = new ArrayList<>();
            
            CommentThreadListResponse response = youtube.commentThreads()
                .list(List.of("snippet"))
                .setVideoId(videoId)
                .setOrder("time") // Order by newest first
                .setMaxResults((long) limit)
                .setKey(apiKey)
                .execute();

            String videoUrl = "https://www.youtube.com/watch?v=" + videoId;

            for (CommentThread thread : response.getItems()) {
                CommentSnippet commentSnippet = thread.getSnippet().getTopLevelComment().getSnippet();
                
                CommentData commentData = new CommentData(
                    videoUrl,
                    videoId,
                    videoTitle,
                    videoThumbnail,
                    commentSnippet.getTextDisplay(),
                    commentSnippet.getLikeCount() != null ? commentSnippet.getLikeCount().longValue() : 0L,
                    commentSnippet.getAuthorDisplayName(),
                    channelMetadata
                );
                
                comments.add(commentData);
                
                if (comments.size() >= limit) break;
            }

            logger.info("Fetched {} newest comments for video: {}", comments.size(), videoTitle);
            return comments;

        } catch (IOException e) {
            logger.error("Error fetching newest comments for video ID: {}", videoId, e);
            return new ArrayList<>();
        }
    }

    /**
     * Data class to hold video information
     */
    public static class VideoData {
        private final String videoId;
        private final String videoTitle;
        private final String videoThumbnail;
        private final ChannelMetadata channelMetadata;
        private final List<CommentData> newestComments;

        public VideoData(String videoId, String videoTitle, String videoThumbnail, 
                        ChannelMetadata channelMetadata, List<CommentData> newestComments) {
            this.videoId = videoId;
            this.videoTitle = videoTitle;
            this.videoThumbnail = videoThumbnail;
            this.channelMetadata = channelMetadata;
            this.newestComments = newestComments;
        }

        public String getVideoId() { return videoId; }
        public String getVideoTitle() { return videoTitle; }
        public String getVideoThumbnail() { return videoThumbnail; }
        public ChannelMetadata getChannelMetadata() { return channelMetadata; }
        public List<CommentData> getNewestComments() { return newestComments; }
    }
} 