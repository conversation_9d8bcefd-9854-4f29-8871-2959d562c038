version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: youtube-mysql
    environment:
      MYSQL_ROOT_PASSWORD: rootpass
      MYSQL_DATABASE: youtube_analytics
      MYSQL_USER: youtube_user
      MYSQL_PASSWORD: youtube_pass
    ports:
      - "3309:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - youtube-network

  # Zookeeper (required for Kafka)
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    container_name: youtube-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"
    networks:
      - youtube-network

  # Kafka
  kafka:
    image: confluentinc/cp-kafka:latest
    container_name: youtube-kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
    networks:
      - youtube-network

  # Producer Web App
  producer-app:
    build: ./producer-app
    container_name: youtube-producer
    ports:
      - "8081:8081"
    depends_on:
      - mysql
      - kafka
    environment:
      SPRING_PROFILES_ACTIVE: docker
      DB_HOST: mysql
      DB_PORT: 3306
      DB_NAME: youtube_analytics
      DB_USER: youtube_user
      DB_PASSWORD: youtube_pass
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
      YOUTUBE_API_KEY: ${YOUTUBE_API_KEY}
    networks:
      - youtube-network

  # Consumer Web App
  consumer-app:
    build: ./consumer-app
    container_name: youtube-consumer
    ports:
      - "8083:8082"
    depends_on:
      - kafka
    environment:
      SPRING_PROFILES_ACTIVE: docker
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
    networks:
      - youtube-network

  # Telegram Bot
  telegram-bot:
    build: ./telegram-bot
    container_name: youtube-telegram-bot
    ports:
      - "8084:8084"
    depends_on:
      - kafka
      - mysql
    environment:
      SPRING_PROFILES_ACTIVE: docker
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
      TELEGRAM_BOT_TOKEN: ${TELEGRAM_BOT_TOKEN}
      DB_HOST: mysql
      DB_PORT: 3306
      DB_NAME: youtube_analytics
      DB_USER: youtube_user
      DB_PASSWORD: youtube_pass
    networks:
      - youtube-network

volumes:
  mysql_data:

networks:
  youtube-network:
    driver: bridge 