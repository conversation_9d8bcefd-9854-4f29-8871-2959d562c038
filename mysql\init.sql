-- YouTube Analytics Database Schema

-- Table to store YouTube video links submitted by users
CREATE TABLE youtube_links (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    video_url VARCHAR(255) NOT NULL UNIQUE,
    video_id VARCHAR(50) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    processing_session VARCHAR(100) DEFAULT NULL,
    metadata_sent BOOLEAN DEFAULT FALSE,
    processed_for_telegram BOOLEAN DEFAULT FALSE
);

-- Table to store channel metadata for analytics
CREATE TABLE channel_metadata (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    video_id VARCHAR(50) NOT NULL,
    channel_name VARCHAR(255) NOT NULL,
    channel_id VARCHAR(50) NOT NULL,
    subscribers_count BIGINT DEFAULT 0,
    total_videos BIGINT DEFAULT 0,
    total_comments BIGINT DEFAULT 0,
    total_likes BIGINT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (video_id) REFERENCES youtube_links(video_id) ON DELETE CASCADE
);

-- Table to store processed comments for tracking
CREATE TABLE processed_comments (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    video_id VARCHAR(50) NOT NULL,
    comment_id VARCHAR(100) NOT NULL UNIQUE,
    comment_text TEXT NOT NULL,
    comment_length INT NOT NULL,
    likes_count BIGINT DEFAULT 0,
    is_odd_length BOOLEAN NOT NULL,
    processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sent_to_telegram BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (video_id) REFERENCES youtube_links(video_id) ON DELETE CASCADE
);

-- Table to store Telegram bot subscribers (persistent across restarts)
CREATE TABLE telegram_subscribers (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    chat_id VARCHAR(50) NOT NULL UNIQUE,
    user_name VARCHAR(255),
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Indexes for better performance
CREATE INDEX idx_youtube_links_video_id ON youtube_links(video_id);
CREATE INDEX idx_youtube_links_active ON youtube_links(is_active);
CREATE INDEX idx_youtube_links_session ON youtube_links(processing_session);
CREATE INDEX idx_channel_metadata_video_id ON channel_metadata(video_id);
CREATE INDEX idx_processed_comments_video_id ON processed_comments(video_id);
CREATE INDEX idx_processed_comments_length ON processed_comments(is_odd_length);
CREATE INDEX idx_telegram_subscribers_chat_id ON telegram_subscribers(chat_id);
CREATE INDEX idx_telegram_subscribers_active ON telegram_subscribers(is_active);

-- Insert some sample data for testing (optional)
-- INSERT INTO youtube_links (video_url, video_id) VALUES 
-- ('https://www.youtube.com/watch?v=dQw4w9WgXcQ', 'dQw4w9WgXcQ'); 