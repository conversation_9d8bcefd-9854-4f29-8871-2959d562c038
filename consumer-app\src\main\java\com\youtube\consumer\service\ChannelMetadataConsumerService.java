package com.youtube.consumer.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

import com.youtube.shared.model.ChannelMetadata;

@Service
public class ChannelMetadataConsumerService {

    private static final Logger logger = LoggerFactory.getLogger(ChannelMetadataConsumerService.class);

    // In-memory storage for channel metadata (deduplicates by channel ID)
    private final Map<String, ChannelMetadata> channelMetadataMap = new ConcurrentHashMap<>();
    private final Map<String, List<String>> channelVideoMapping = new ConcurrentHashMap<>();
    private volatile boolean newDataReceived = false;

    /**
     * Consume even-length metadata from Kafka and store for analytics
     */
    @KafkaListener(topics = "youtube-even-metadata", groupId = "youtube-analytics-group-v2")
    public void consumeChannelMetadata(ChannelMetadata metadata) {
        logger.info("Received channel metadata from Kafka: Channel '{}' for video '{}'", 
                   metadata.getChannelName(), metadata.getVideoId());

        try {
            String channelId = metadata.getChannelId();
            
            // Store or update channel metadata (deduplicates by channel ID)
            if (channelMetadataMap.containsKey(channelId)) {
                logger.debug("Updating existing channel metadata for channel: {}", metadata.getChannelName());
                // Keep the existing metadata but track this video as well
                ChannelMetadata existing = channelMetadataMap.get(channelId);
                // Update with latest stats if this video has newer data
                if (metadata.getLastUpdated() != null && 
                    (existing.getLastUpdated() == null || metadata.getLastUpdated().isAfter(existing.getLastUpdated()))) {
                    channelMetadataMap.put(channelId, metadata);
                }
            } else {
                logger.info("Adding new channel metadata for channel: {}", metadata.getChannelName());
                channelMetadataMap.put(channelId, metadata);
                channelVideoMapping.put(channelId, new ArrayList<>());
            }
            
            // Track which video belongs to this channel
            List<String> videos = channelVideoMapping.get(channelId);
            if (!videos.contains(metadata.getVideoId())) {
                videos.add(metadata.getVideoId());
                logger.debug("Added video {} to channel {} (total videos from this channel: {})", 
                           metadata.getVideoId(), metadata.getChannelName(), videos.size());
            }

            // Mark that new data has been received
            newDataReceived = true;
            
            logger.debug("Successfully processed channel metadata. Total unique channels: {}", 
                       channelMetadataMap.size());

        } catch (Exception e) {
            logger.error("Error processing channel metadata from Kafka", e);
        }
    }

    /**
     * Get all unique channel metadata (one per channel)
     */
    public List<ChannelMetadata> getAllChannelMetadata() {
        return new ArrayList<>(channelMetadataMap.values());
    }

    /**
     * Get metadata for a specific channel
     */
    public ChannelMetadata getChannelMetadata(String channelId) {
        return channelMetadataMap.get(channelId);
    }

    /**
     * Get video count for a specific channel
     */
    public int getVideoCountForChannel(String channelId) {
        List<String> videos = channelVideoMapping.get(channelId);
        return videos != null ? videos.size() : 0;
    }

    /**
     * Get all video IDs for a specific channel
     */
    public List<String> getVideosForChannel(String channelId) {
        return channelVideoMapping.getOrDefault(channelId, new ArrayList<>());
    }

    /**
     * Clear all stored data (when new submission arrives)
     */
    public void clearAll() {
        logger.info("Clearing all channel metadata and video mappings");
        int channelCount = channelMetadataMap.size();
        int totalVideos = channelVideoMapping.values().stream().mapToInt(List::size).sum();
        
        channelMetadataMap.clear();
        channelVideoMapping.clear();
        newDataReceived = false;
        
        logger.info("Cleared {} channels and {} video mappings", channelCount, totalVideos);
    }

    /**
     * Check if new data has been received since last check
     */
    public boolean hasNewData() {
        return newDataReceived;
    }

    /**
     * Mark data as processed (reset new data flag)
     */
    public void markDataAsProcessed() {
        newDataReceived = false;
    }

    /**
     * Get summary statistics
     */
    public Map<String, Object> getStats() {
        int totalChannels = channelMetadataMap.size();
        int totalVideos = channelVideoMapping.values().stream().mapToInt(List::size).sum();
        
        return Map.of(
            "totalChannels", totalChannels,
            "totalVideos", totalVideos,
            "newDataReceived", newDataReceived,
            "channels", channelMetadataMap.keySet()
        );
    }
} 