# 🚀 YouTube Analytics System - User Guide

## 📋 Prerequisites

1. **Docker & Docker Compose** installed on your system
2. **YouTube Data API v3 Key** (get from [Google Cloud Console](https://console.developers.google.com/))
3. **Telegram Bot Token** (get from [@BotFather](https://t.me/BotFather) on Telegram)

## 🔧 Setup Instructions

### Step 1: Configure Environment Variables

1. Make sure your `.env` file exists in the project root with:
```env
YOUTUBE_API_KEY=your_youtube_api_key_here
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
```

### Step 2: Build the Applications

First, build the JAR files for all applications:

```bash
# Build shared module first
cd shared
mvn clean install -DskipTests
cd ..

# Build producer app
cd producer-app
mvn clean package -DskipTests
cd ..

# Build consumer app
cd consumer-app
mvn clean package -DskipTests
cd ..

# Build telegram bot
cd telegram-bot
mvn clean package -DskipTests
cd ..
```

### Step 3: Start All Services

Open terminal in the project root directory and run:

```bash
# Build and start all 6 containers
docker-compose up --build -d
```

This command will start:
- 🗄️ **MySQL Database** (Port 3309)
- 🔄 **Zookeeper** (Port 2181) 
- 📨 **Kafka** (Port 9092)
- 🌐 **Producer Web App** (Port 8081)
- 📊 **Consumer Web App** (Port 8083)
- 🤖 **Telegram Bot** (Port 8084)

### Step 4: Verify All Services Are Running

```bash
# Check if all containers are running
docker-compose ps
```

You should see all 6 services with "Up" status.

### Step 5: Wait for Services to Initialize

Wait about 2-3 minutes for all services to fully start up, especially:
- MySQL database initialization
- Kafka topic creation
- Spring Boot applications startup

## 🎯 How to Use the System

### 1. Access the Producer App
- Open your browser and go to: **http://localhost:8081**
- You'll see a clean interface with a form to submit 5 YouTube video links

### 2. Submit YouTube Videos
- Enter exactly **5 YouTube video URLs** in the form
- Click "Submit Links"
- The system will start processing automatically

### 3. View Analytics
- Open another browser tab and go to: **http://localhost:8083**
- This is the Consumer App where you can view analytics
- Click "Calculate Analytics" to see the comparison of your 5 videos

### 4. Telegram Bot
- Open Telegram on your phone/desktop
- Search for your bot using the bot username
- Start the bot with `/start`
- You'll receive odd-length comments automatically

## 🔄 System Workflow

1. **Every 30 seconds**, the system fetches the 5 newest comments from each video
2. **New comments only** are processed (no duplicates/spam)
3. **Odd-length comments** → Sent to Telegram Bot
4. **Even-length comments** → Trigger metadata to Consumer App for analytics
5. **Fresh metadata** is sent every 30 seconds to keep analytics up-to-date

## 🛠️ Useful Commands

### Check Logs
```bash
# View logs for all services
docker-compose logs

# View logs for specific service
docker-compose logs producer-app
docker-compose logs consumer-app
docker-compose logs telegram-bot
```

### Restart Services
```bash
# Restart all services
docker-compose restart

# Restart specific service
docker-compose restart producer-app
```

### Stop All Services
```bash
# Stop all containers
docker-compose down
```

### Reset Everything (Fresh Start)
```bash
# Stop containers and remove volumes
docker-compose down -v

# Remove all images (optional)
docker-compose down --rmi all

# Start fresh
docker-compose up --build -d
```

## 🌐 Access URLs

- **Producer App**: http://localhost:8081
- **Consumer App**: http://localhost:8083  
- **Telegram Bot**: Search your bot on Telegram app
- **MySQL**: localhost:3309 (if you need direct database access)

## 🔍 Troubleshooting

### If containers fail to start:
1. Check if ports are available: `netstat -an | findstr "8081\|8083\|8084\|3309\|9092"`
2. Check Docker logs: `docker-compose logs [service-name]`
3. Verify `.env` file has correct API keys

### If YouTube API fails:
1. Verify your YouTube API key is valid
2. Check API quota limits in Google Cloud Console
3. Ensure YouTube Data API v3 is enabled

### If Telegram bot doesn't respond:
1. Verify bot token is correct
2. Make sure bot is not already running elsewhere
3. Check telegram-bot service logs

## 🎉 Success Indicators

✅ **Producer App**: Shows "Active Video Links: 5" after submission  
✅ **Consumer App**: Displays channel metadata and analytics  
✅ **Telegram Bot**: Sends you odd-length comments  
✅ **System**: New comments processed every 30 seconds automatically

## 📝 Notes

- **First run** may take 3-5 minutes for all services to fully initialize
- **Database** is automatically created with the correct schema
- **Kafka topics** are auto-created when first message is sent
- **System resets** when you submit a new set of 5 videos
- **Only new comments** are processed to avoid spam
