# 🚀 YouTube Analytics System - User Guide

## 📋 Prerequisites

1. **Docker & Docker Compose** installed on your system
2. **YouTube Data API v3 Key** (get from [Google Cloud Console](https://console.developers.google.com/))
3. **Telegram Bot Token** (get from [@Bot<PERSON>ather](https://t.me/BotFather) on Telegram)

## 🔧 Complete System Startup Guide

### Step 1: Configure Environment Variables

1. Create or verify your `.env` file exists in the project root with:
```env
YOUTUBE_API_KEY=your_youtube_api_key_here
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
```

**⚠️ Important**: Make sure your YouTube API key has sufficient quota (resets daily at midnight Pacific Time)

### Step 2: Build All Applications

**Option A: Quick Build (Recommended)**
```bash
# Build and start everything in one command
docker-compose up --build -d
```

**Option B: Manual Build (For Development)**
```bash
# Build shared module first
cd shared
mvn clean install -DskipTests
cd ..

# Build producer app
cd producer-app
mvn clean package -DskipTests
cd ..

# Build consumer app
cd consumer-app
mvn clean package -DskipTests
cd ..

# Build telegram bot
cd telegram-bot
mvn clean package -DskipTests
cd ..

# Then start services
docker-compose up -d
```

### Step 3: Monitor System Startup

```bash
# Check if all containers are running
docker-compose ps
```

**Expected Output:**
```
NAME                   STATUS                     PORTS
youtube-consumer       Up (healthy)               0.0.0.0:8083->8082/tcp
youtube-kafka          Up (healthy)               0.0.0.0:9092->9092/tcp
youtube-mysql          Up                         0.0.0.0:3309->3306/tcp
youtube-producer       Up (healthy)               0.0.0.0:8081->8081/tcp
youtube-telegram-bot   Up (healthy)               0.0.0.0:8084->8084/tcp
youtube-zookeeper      Up (healthy)               0.0.0.0:2181->2181/tcp
```

### Step 4: Wait for Full Initialization

**⏱️ Startup Timeline:**
- **0-30 seconds**: MySQL, Zookeeper start
- **30-60 seconds**: Kafka starts and creates topics
- **60-120 seconds**: Spring Boot applications start
- **120+ seconds**: System ready for use

**Monitor startup progress:**
```bash
# Watch all logs in real-time
docker-compose logs -f

# Watch specific service
docker-compose logs -f telegram-bot
```

### Step 5: Verify System Health

```bash
# Test Producer App
curl http://localhost:8081

# Test Consumer App
curl http://localhost:8083

# Check Telegram Bot logs for "Application ready"
docker-compose logs telegram-bot | grep "Application ready"
```

## 🎯 Complete Usage Guide

### 1. Setup Telegram Bot (First Time Only)

**Step 1.1: Find Your Bot**
- Open Telegram on your phone/desktop
- Search for your bot using the bot username (from @BotFather)
- Start a chat with your bot

**Step 1.2: Initialize Bot**
```
/start
```
**Expected Response:** Welcome message with instructions

**Step 1.3: Subscribe to Notifications**
```
/subscribe
```
**Expected Response:** "You have been subscribed to receive YouTube comment notifications!"

### 2. Submit YouTube Videos

**Step 2.1: Access Producer App**
- Open browser: **http://localhost:8081**
- You'll see a clean interface with 5 input fields

**Step 2.2: Enter Video URLs**
- Enter exactly **5 YouTube video URLs** (any format works):
  - `https://www.youtube.com/watch?v=VIDEO_ID`
  - `https://youtu.be/VIDEO_ID`
  - Just the video ID: `VIDEO_ID`

**Step 2.3: Submit and Monitor**
- Click "Submit Links"
- Page will show "Active Video Links: 5"
- System automatically starts processing every 30 seconds

### 3. View Real-Time Analytics

**Step 3.1: Access Consumer App**
- Open new browser tab: **http://localhost:8083**
- Initially shows "No active videos"

**Step 3.2: Wait for Data**
- After submitting videos, wait 30-60 seconds
- Page will automatically show channel metadata for your 5 videos
- Data refreshes every 30 seconds automatically

**Step 3.3: Calculate Analytics**
- Click "Calculate Analytics" button
- View comprehensive comparison of your 5 videos
- Analytics stay visible until you close them manually
- Click "X" button to hide analytics when done

### 4. Receive Telegram Notifications

**What You'll Receive:**
- **Odd-length comments** from your 5 videos
- **Real-time notifications** as new comments arrive
- **Batch processing** - comments sent in groups every 30 seconds

**Sample Telegram Message:**
```
🎬 New Comments from Video 1/5

📺 Channel: Example Channel
🎥 Video: "Amazing Video Title"
👀 Views: 1,234,567 | 👍 Likes: 12,345

💬 New Comments:
• "Great video!" (11 chars)
• "Amazing content, keep it up!" (29 chars)

⏰ Processed at: 2024-06-02 21:45:30
```

## 🔄 System Workflow & Timing

### Real-Time Processing Cycle (Every 30 seconds)
1. **Fetch Comments**: Get 5 newest comments from each video
2. **Filter New**: Only process comments not seen before
3. **Route by Length**:
   - **Odd-length** → Telegram Bot
   - **Even-length** → Trigger metadata refresh
4. **Update Analytics**: Fresh metadata sent to Consumer App
5. **Repeat**: Continuous 30-second cycle

### Fresh Start Behavior
- **New video submission**: Clears all previous data
- **Docker restart**: Complete system reset
- **Consumer App**: Shows only latest 5 videos
- **Telegram Bot**: No old comments after restart

## 🛠️ Essential Commands

### System Management
```bash
# Start system (recommended)
docker-compose up --build -d

# Check system status
docker-compose ps

# Stop system
docker-compose down

# Complete reset (fresh start)
docker-compose down -v && docker-compose up --build -d
```

### Monitoring & Debugging
```bash
# View all logs in real-time
docker-compose logs -f

# View specific service logs
docker-compose logs -f producer-app
docker-compose logs -f consumer-app
docker-compose logs -f telegram-bot

# View recent logs only
docker-compose logs --tail 50 telegram-bot

# Check for errors
docker-compose logs | grep -i error
```

### Service Management
```bash
# Restart all services
docker-compose restart

# Restart specific service
docker-compose restart telegram-bot

# Rebuild specific service
docker-compose up --build -d telegram-bot

# View service health
docker-compose ps | grep healthy
```

## 🌐 Access URLs & Ports

| Service | URL | Purpose |
|---------|-----|---------|
| **Producer App** | http://localhost:8081 | Submit YouTube videos |
| **Consumer App** | http://localhost:8083 | View analytics |
| **Telegram Bot** | Search bot on Telegram | Receive notifications |
| **MySQL Database** | localhost:3309 | Direct database access |
| **Kafka** | localhost:9092 | Message broker |

## 🔍 Comprehensive Troubleshooting

### 🚨 Common Issues & Solutions

#### 1. YouTube API Quota Exceeded
**Symptoms:**
- Producer logs show "403 Forbidden" and "quotaExceeded"
- No data flows to Consumer or Telegram

**Solutions:**
```bash
# Check producer logs for quota errors
docker-compose logs producer-app | grep -i quota

# Wait for quota reset (daily at midnight Pacific Time)
# OR get new API key from Google Cloud Console
```

#### 2. Containers Won't Start
**Symptoms:**
- `docker-compose ps` shows "Exited" status
- Port conflicts

**Solutions:**
```bash
# Check port availability (Windows)
netstat -an | findstr "8081 8083 8084 3309 9092"

# Check port availability (Linux/Mac)
netstat -tuln | grep -E "8081|8083|8084|3309|9092"

# Kill processes using ports
# Windows: taskkill /F /PID <pid>
# Linux/Mac: kill -9 <pid>

# Restart with fresh build
docker-compose down -v
docker-compose up --build -d
```

#### 3. Telegram Bot Not Responding
**Symptoms:**
- Bot doesn't reply to `/start` or `/subscribe`
- No notifications received

**Solutions:**
```bash
# Check bot logs for errors
docker-compose logs telegram-bot | grep -i error

# Verify bot token in .env file
cat .env | grep TELEGRAM_BOT_TOKEN

# Restart bot service
docker-compose restart telegram-bot

# Check if bot is subscribed
docker-compose logs telegram-bot | grep "subscribed"
```

#### 4. No Data After Video Submission
**Symptoms:**
- Producer shows "Active Video Links: 5"
- Consumer shows "No active videos"
- No Telegram notifications

**Diagnostic Steps:**
```bash
# 1. Check producer logs for API errors
docker-compose logs producer-app | tail -20

# 2. Check Kafka connectivity
docker-compose logs kafka | grep -i error

# 3. Check consumer logs
docker-compose logs consumer-app | tail -20

# 4. Check telegram bot logs
docker-compose logs telegram-bot | tail -20
```

#### 5. System Performance Issues
**Symptoms:**
- Slow response times
- Services marked as "unhealthy"

**Solutions:**
```bash
# Check system resources
docker stats

# Restart unhealthy services
docker-compose ps | grep unhealthy
docker-compose restart <service-name>

# Increase Docker memory allocation (Docker Desktop)
# Settings > Resources > Memory > Increase to 4GB+
```

## 🎉 Success Indicators & Expected Behavior

### ✅ System Startup Success
```bash
# All services healthy
docker-compose ps
# Should show all services as "Up (healthy)"

# Telegram bot ready
docker-compose logs telegram-bot | grep "Application ready"
# Should show: "Application ready - performing fresh start reset..."
```

### ✅ Video Submission Success
- **Producer App**: Shows "Active Video Links: 5" after submission
- **Producer Logs**: No "403 Forbidden" or quota errors
- **Consumer App**: Displays channel metadata within 60 seconds
- **Telegram Bot**: Sends welcome message about new processing session

### ✅ Real-Time Processing Success
- **Every 30 seconds**: New comments processed automatically
- **Consumer App**: Channel metadata updates automatically
- **Telegram Bot**: Sends odd-length comments in batches
- **Analytics**: Calculate button works and shows comparison data

### ✅ Fresh Start Success (After Docker Restart)
- **Telegram Bot**: No old comments sent after restart
- **Consumer App**: Shows only new videos, not previous ones
- **System**: Complete reset of all previous data

## 🆕 Latest Features & Improvements

### 🔄 Fresh Start System
- **Complete Reset on Docker Restart**: No old data persists
- **Kafka Offset Reset**: Telegram bot ignores old messages
- **Clean Slate**: Each restart provides fresh start experience
- **New Video Submission Reset**: Clears all previous data automatically

### 📊 Enhanced Analytics
- **Persistent Analytics**: Stay visible until manually closed
- **Close Button**: X button in top-right corner of analytics panel
- **Auto-refresh Protection**: Page won't reload while analytics displayed
- **Real-time Metadata**: Channel data updates every 30 seconds

### 🛡️ Improved Reliability
- **Health Checks**: All services wait for dependencies
- **Auto-restart**: Services restart automatically if they fail
- **Robust Kafka**: Better connection handling and error recovery
- **Database Persistence**: MySQL data survives container restarts

### 🤖 Enhanced Telegram Bot
- **Batch Processing**: Comments sent in organized batches
- **Rich Formatting**: Detailed video metadata with each comment
- **Fresh Start**: No old comments after system restart
- **Better Error Handling**: More robust message processing

## 📝 Important Notes

### ⏱️ Timing Expectations
- **First startup**: 2-3 minutes for full initialization
- **Video processing**: 30-60 seconds for first data to appear
- **Comment processing**: Every 30 seconds automatically
- **Analytics calculation**: Instant when button clicked

### 🔄 System Behavior
- **Fresh start**: Every Docker restart clears all previous data
- **New videos**: Submitting new videos clears previous ones
- **Comment filtering**: Only NEW comments processed (no duplicates)
- **Real-time updates**: Metadata refreshes every 30 seconds

### 🎯 Best Practices
- **Wait for health checks**: Ensure all services show "healthy" status
- **Monitor logs**: Use `docker-compose logs -f` to watch system activity
- **Check API quota**: Ensure YouTube API has sufficient daily quota
- **Fresh start**: Use `docker-compose down -v && docker-compose up --build -d` for complete reset

### 🚨 When to Restart System
- **YouTube API quota exceeded**: Wait for daily reset or get new key
- **Services showing unhealthy**: Restart specific service or entire system
- **Unexpected behavior**: Complete fresh start usually resolves issues
- **After code changes**: Always rebuild with `--build` flag
